import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage, updateProcessedImage, checkDuplicateRecords, getDatabaseStats } from "@/lib/supabase";
import { removeBackground } from "@/lib/runware";
import { ApiResponse } from "@/types";

export async function POST(request: NextRequest) {
  console.log(`[API] POST /api/process/remove-background - Request received`);

  try {
    const requestBody = await request.json();
    console.log(`[API] Request body:`, requestBody);

    const { imageId } = requestBody;

    if (!imageId) {
      console.error(`[API] Missing imageId in request`);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    console.log(`[API] Processing background removal for imageId: ${imageId}`);

    // Check database health before proceeding
    console.log(`[API] Checking database health...`);
    await getDatabaseStats();
    await checkDuplicateRecords();

    // Get image record from database
    console.log(`[API] Fetching image record from database...`);
    const imageRecord = await getProcessedImage(imageId);

    if (!imageRecord) {
      console.error(`[API] Image not found in database for imageId: ${imageId}`);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    console.log(`[API] Image record found:`, {
      id: imageRecord.id,
      original_url: imageRecord.original_url,
      status: imageRecord.status
    });

    // Update status to processing
    console.log(`[API] Updating status to processing_background_removal...`);
    await updateProcessedImage(imageId, {
      status: "processing_background_removal",
    });

    try {
      // Remove background using Runware API
      console.log(`[API] Calling Runware API for background removal...`);
      console.log(`[API] Original image URL: ${imageRecord.original_url}`);

      const backgroundRemovedUrl = await removeBackground(imageRecord.original_url);

      console.log(`[API] Background removal successful. New URL: ${backgroundRemovedUrl}`);

      // Update database with result
      console.log(`[API] Updating database with result...`);
      const updatedRecord = await updateProcessedImage(imageId, {
        background_removed_url: backgroundRemovedUrl,
        status: "background_removed",
      });

      console.log(`[API] Database updated successfully`);

      const response = {
        success: true,
        data: {
          imageId: updatedRecord.id,
          backgroundRemovedUrl: backgroundRemovedUrl,
          status: updatedRecord.status,
        },
        message: "Background removed successfully",
      };

      console.log(`[API] Sending success response:`, response);
      return NextResponse.json<ApiResponse>(response);

    } catch (runwareError) {
      console.error(`[API] Runware API error:`, runwareError);

      // Update status to error
      console.log(`[API] Updating status to error...`);
      await updateProcessedImage(imageId, {
        status: "error",
      });

      throw runwareError;
    }

  } catch (error) {
    console.error("[API] Remove background error:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("[API] Error message:", error.message);
      console.error("[API] Error stack:", error.stack);
    }

    const errorResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Background removal failed",
    };

    console.log("[API] Sending error response:", errorResponse);

    return NextResponse.json<ApiResponse>(errorResponse, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
